// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'api/minimal.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart' if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


                /// Main entrypoint of the Rust API
                class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
                  @internal
                  static final instance = RustLib._();

                  RustLib._();

                  /// Initialize flutter_rust_bridge
                  static Future<void> init({
                    RustLibApi? api,
                    BaseHandler? handler,
                    ExternalLibrary? externalLibrary,
                    bool forceSameCodegenVersion = true,
                  }) async {
                    await instance.initImpl(
                      api: api,
                      handler: handler,
                      externalLibrary: externalLibrary,
                      forceSameCodegenVersion: forceSameCodegenVersion,
                    );
                  }

                  /// Initialize flutter_rust_bridge in mock mode.
                  /// No libraries for FFI are loaded.
                  static void initMock({
                    required RustLibApi api,
                  }) {
                    instance.initMockImpl(
                      api: api,
                    );
                  }

                  /// Dispose flutter_rust_bridge
                  ///
                  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
                  /// is automatically disposed when the app stops.
                  static void dispose() => instance.disposeImpl();

                  @override
                  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor => RustLibApiImpl.new;

                  @override
                  WireConstructor<RustLibWire> get wireConstructor => RustLibWire.fromExternalLibrary;

                  @override
                  Future<void> executeRustInitializers() async {
                    await api.crateApiMinimalInitApp();

                  }

                  @override
                  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig => kDefaultExternalLibraryLoaderConfig;

                  @override
                  String get codegenVersion => '2.10.0';

                  @override
                  int get rustContentHash => -2119384465;

                  static const kDefaultExternalLibraryLoaderConfig = ExternalLibraryLoaderConfig(
                    stem: 'frb_example_dart_minimal',
                    ioDirectory: 'rust/target/release/',
                    webPrefix: 'pkg/',
                  );
                }
                

                abstract class RustLibApi extends BaseApi {
                  Future<void> crateApiMinimalInitApp();

Future<int> crateApiMinimalMinimalAdder({required int a , required int b });


                }
                

                class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
                  RustLibApiImpl({
                    required super.handler,
                    required super.wire,
                    required super.generalizedFrbRustBinding,
                    required super.portManager,
                  });

                  @override Future<void> crateApiMinimalInitApp()  { return handler.executeNormal(NormalTask(
            callFfi: (port_) {
              
            final serializer = SseSerializer(generalizedFrbRustBinding);
            pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1, port: port_);
            
            },
            codec: 
        SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: null,
        )
        ,
            constMeta: kCrateApiMinimalInitAppConstMeta,
            argValues: [],
            apiImpl: this,
        )); }


        TaskConstMeta get kCrateApiMinimalInitAppConstMeta => const TaskConstMeta(
            debugName: "init_app",
            argNames: [],
        );
        

@override Future<int> crateApiMinimalMinimalAdder({required int a , required int b })  { return handler.executeNormal(NormalTask(
            callFfi: (port_) {
              
            final serializer = SseSerializer(generalizedFrbRustBinding);sse_encode_i_32(a, serializer);
sse_encode_i_32(b, serializer);
            pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2, port: port_);
            
            },
            codec: 
        SseCodec(
          decodeSuccessData: sse_decode_i_32,
          decodeErrorData: null,
        )
        ,
            constMeta: kCrateApiMinimalMinimalAdderConstMeta,
            argValues: [a, b],
            apiImpl: this,
        )); }


        TaskConstMeta get kCrateApiMinimalMinimalAdderConstMeta => const TaskConstMeta(
            debugName: "minimal_adder",
            argNames: ["a", "b"],
        );
        



                  @protected int dco_decode_i_32(dynamic raw){ // Codec=Dco (DartCObject based), see doc to use other codecs
return raw as int; }

@protected void dco_decode_unit(dynamic raw){ // Codec=Dco (DartCObject based), see doc to use other codecs
return; }

@protected int sse_decode_i_32(SseDeserializer deserializer){ // Codec=Sse (Serialization based), see doc to use other codecs
return deserializer.buffer.getInt32(); }

@protected void sse_decode_unit(SseDeserializer deserializer){ // Codec=Sse (Serialization based), see doc to use other codecs
 }

@protected bool sse_decode_bool(SseDeserializer deserializer){ // Codec=Sse (Serialization based), see doc to use other codecs
return deserializer.buffer.getUint8() != 0; }

@protected void sse_encode_i_32(int self, SseSerializer serializer){ // Codec=Sse (Serialization based), see doc to use other codecs
serializer.buffer.putInt32(self); }

@protected void sse_encode_unit(void self, SseSerializer serializer){ // Codec=Sse (Serialization based), see doc to use other codecs
 }

@protected void sse_encode_bool(bool self, SseSerializer serializer){ // Codec=Sse (Serialization based), see doc to use other codecs
serializer.buffer.putUint8(self ? 1 : 0); }
                }
                
